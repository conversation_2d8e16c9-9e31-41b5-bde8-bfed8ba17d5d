/** @generated THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY. */
import * as Types from '../models.gen';

import { gql } from '@apollo/client';
import * as Apollo from '@apollo/client';
const defaultOptions = {} as const;
export type CashierApplicationMutationVariables = Types.Exact<{
  merchantId: Types.Scalars['Int']['input'];
  amount: Types.Scalars['Float']['input'];
  merchantDownPayment: Types.Scalars['Float']['input'];
  reference: Types.Scalars['String']['input'];
  message?: Types.InputMaybe<Types.Scalars['String']['input']>;
  fromRetail: Types.Scalars['Boolean']['input'];
  phone?: Types.InputMaybe<Types.Scalars['String']['input']>;
  specialSettings: Types.CashierApplicationSpecialSettings;
  pin?: Types.InputMaybe<Types.Scalars['String']['input']>;
  documentType?: Types.InputMaybe<Types.UserDocumentType>;
  documentNr?: Types.InputMaybe<Types.Scalars['String']['input']>;
  storeId?: Types.InputMaybe<Types.Scalars['Int']['input']>;
  isTest?: Types.InputMaybe<Types.Scalars['Boolean']['input']>;
  signingMethod?: Types.InputMaybe<Types.SigningMethod>;
  scheduleType?: Types.InputMaybe<Types.ApplicationScheduleType>;
  firstName?: Types.InputMaybe<Types.Scalars['String']['input']>;
  lastName?: Types.InputMaybe<Types.Scalars['String']['input']>;
  email?: Types.InputMaybe<Types.Scalars['String']['input']>;
  languageAbbr?: Types.InputMaybe<Types.Scalars['String']['input']>;
}>;

export type CashierApplicationMutation = {
  __typename?: 'Mutation';
  application?: {
    __typename?: 'Application';
    id: number;
    purchase_url: string;
  } | null;
};

export type SendPurchaseUrlMutationVariables = Types.Exact<{
  applicationId: Types.Scalars['Int']['input'];
  type?: Types.InputMaybe<Types.UserMessageSentType>;
  phone?: Types.InputMaybe<Types.Scalars['String']['input']>;
  email?: Types.InputMaybe<Types.Scalars['String']['input']>;
}>;

export type SendPurchaseUrlMutation = {
  __typename?: 'Mutation';
  sent: boolean;
};

export const CashierApplicationDocument = gql`
  mutation cashierApplication(
    $merchantId: Int!
    $amount: Float!
    $merchantDownPayment: Float!
    $reference: String!
    $message: String
    $fromRetail: Boolean!
    $phone: String
    $specialSettings: cashier_application_special_settings!
    $pin: String
    $documentType: UserDocumentType
    $documentNr: String
    $storeId: Int
    $isTest: Boolean
    $signingMethod: signing_method
    $scheduleType: ApplicationScheduleType
    $firstName: String
    $lastName: String
    $email: String
    $languageAbbr: String
  ) {
    application: cashier_application(
      merchant_id: $merchantId
      amount: $amount
      merchant_down_payment: $merchantDownPayment
      reference: $reference
      message: $message
      from_retail: $fromRetail
      phone: $phone
      special_settings: $specialSettings
      pin: $pin
      document_type: $documentType
      document_nr: $documentNr
      store_id: $storeId
      is_test: $isTest
      signing_method: $signingMethod
      schedule_type: $scheduleType
      first_name: $firstName
      last_name: $lastName
      email: $email
      language_abbr: $languageAbbr
    ) {
      id
      purchase_url
    }
  }
`;
export type CashierApplicationMutationFn = Apollo.MutationFunction<
  CashierApplicationMutation,
  CashierApplicationMutationVariables
>;

/**
 * __useCashierApplicationMutation__
 *
 * To run a mutation, you first call `useCashierApplicationMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useCashierApplicationMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [cashierApplicationMutation, { data, loading, error }] = useCashierApplicationMutation({
 *   variables: {
 *      merchantId: // value for 'merchantId'
 *      amount: // value for 'amount'
 *      merchantDownPayment: // value for 'merchantDownPayment'
 *      reference: // value for 'reference'
 *      message: // value for 'message'
 *      fromRetail: // value for 'fromRetail'
 *      phone: // value for 'phone'
 *      specialSettings: // value for 'specialSettings'
 *      pin: // value for 'pin'
 *      documentType: // value for 'documentType'
 *      documentNr: // value for 'documentNr'
 *      storeId: // value for 'storeId'
 *      isTest: // value for 'isTest'
 *      signingMethod: // value for 'signingMethod'
 *      scheduleType: // value for 'scheduleType'
 *      firstName: // value for 'firstName'
 *      lastName: // value for 'lastName'
 *      email: // value for 'email'
 *      languageAbbr: // value for 'languageAbbr'
 *   },
 * });
 */
export function useCashierApplicationMutation(
  baseOptions?: Apollo.MutationHookOptions<
    CashierApplicationMutation,
    CashierApplicationMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    CashierApplicationMutation,
    CashierApplicationMutationVariables
  >(CashierApplicationDocument, options);
}
export type CashierApplicationMutationHookResult = ReturnType<
  typeof useCashierApplicationMutation
>;
export type CashierApplicationMutationResult =
  Apollo.MutationResult<CashierApplicationMutation>;
export type CashierApplicationMutationOptions = Apollo.BaseMutationOptions<
  CashierApplicationMutation,
  CashierApplicationMutationVariables
>;
export const SendPurchaseUrlDocument = gql`
  mutation sendPurchaseUrl(
    $applicationId: Int!
    $type: UserMessageSentType
    $phone: String
    $email: String
  ) {
    sent: send_purchase_url(
      application_id: $applicationId
      type: $type
      phone: $phone
      email: $email
    )
  }
`;
export type SendPurchaseUrlMutationFn = Apollo.MutationFunction<
  SendPurchaseUrlMutation,
  SendPurchaseUrlMutationVariables
>;

/**
 * __useSendPurchaseUrlMutation__
 *
 * To run a mutation, you first call `useSendPurchaseUrlMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useSendPurchaseUrlMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [sendPurchaseUrlMutation, { data, loading, error }] = useSendPurchaseUrlMutation({
 *   variables: {
 *      applicationId: // value for 'applicationId'
 *      type: // value for 'type'
 *      phone: // value for 'phone'
 *      email: // value for 'email'
 *   },
 * });
 */
export function useSendPurchaseUrlMutation(
  baseOptions?: Apollo.MutationHookOptions<
    SendPurchaseUrlMutation,
    SendPurchaseUrlMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    SendPurchaseUrlMutation,
    SendPurchaseUrlMutationVariables
  >(SendPurchaseUrlDocument, options);
}
export type SendPurchaseUrlMutationHookResult = ReturnType<
  typeof useSendPurchaseUrlMutation
>;
export type SendPurchaseUrlMutationResult =
  Apollo.MutationResult<SendPurchaseUrlMutation>;
export type SendPurchaseUrlMutationOptions = Apollo.BaseMutationOptions<
  SendPurchaseUrlMutation,
  SendPurchaseUrlMutationVariables
>;
