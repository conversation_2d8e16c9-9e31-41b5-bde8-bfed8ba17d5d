/** @generated THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY. */
import * as Types from '../models.gen';

import { gql } from '@apollo/client';
import * as Apollo from '@apollo/client';
const defaultOptions = {} as const;
export type MerchantFragment = {
  __typename?: 'UserMerchant';
  id: number;
  name: string;
  merchant_permission_bits: number;
  send_emails: number;
  logo_path?: string | null;
  settings?: {
    __typename?: 'MerchantSettings';
    merchant_financing_pct: number;
  } | null;
  stores?: Array<{
    __typename?: 'MerchantStore';
    id: number;
    name: string;
  } | null> | null;
};

export type CustomerFragment = {
  __typename: 'User';
  id: number;
  email?: string | null;
  pin?: string | null;
  is_password_set: boolean;
  language_abbr: string;
  permission_bits: number;
  profile?: {
    __typename?: 'UserProfile';
    first_name?: string | null;
    last_name?: string | null;
  } | null;
  merchants?: Array<{
    __typename?: 'UserMerchant';
    id: number;
    name: string;
    merchant_permission_bits: number;
    send_emails: number;
    logo_path?: string | null;
    settings?: {
      __typename?: 'MerchantSettings';
      merchant_financing_pct: number;
    } | null;
    stores?: Array<{
      __typename?: 'MerchantStore';
      id: number;
      name: string;
    } | null> | null;
  } | null> | null;
};

export type UserDataQueryVariables = Types.Exact<{ [key: string]: never }>;

export type UserDataQuery = {
  __typename?: 'Query';
  me?: {
    __typename: 'User';
    id: number;
    email?: string | null;
    pin?: string | null;
    is_password_set: boolean;
    language_abbr: string;
    permission_bits: number;
    profile?: {
      __typename?: 'UserProfile';
      first_name?: string | null;
      last_name?: string | null;
    } | null;
    merchants?: Array<{
      __typename?: 'UserMerchant';
      id: number;
      name: string;
      merchant_permission_bits: number;
      send_emails: number;
      logo_path?: string | null;
      settings?: {
        __typename?: 'MerchantSettings';
        merchant_financing_pct: number;
      } | null;
      stores?: Array<{
        __typename?: 'MerchantStore';
        id: number;
        name: string;
      } | null> | null;
    } | null> | null;
  } | null;
};

export type LogoutMutationVariables = Types.Exact<{ [key: string]: never }>;

export type LogoutMutation = {
  __typename?: 'Mutation';
  logout?: boolean | null;
};

export type UpdateUserLanguageMutationVariables = Types.Exact<{
  languageAbbr: Types.Scalars['String']['input'];
  userId: Types.Scalars['Int']['input'];
}>;

export type UpdateUserLanguageMutation = {
  __typename?: 'Mutation';
  succes: boolean;
};

export const MerchantFragmentDoc = gql`
  fragment Merchant on UserMerchant {
    id
    name
    merchant_permission_bits
    send_emails
    settings {
      merchant_financing_pct
    }
    logo_path
    stores {
      id
      name
    }
  }
`;
export const CustomerFragmentDoc = gql`
  fragment Customer on User {
    __typename
    id
    email
    pin
    is_password_set
    language_abbr
    profile {
      first_name
      last_name
    }
    permission_bits
    merchants {
      ...Merchant
    }
  }
  ${MerchantFragmentDoc}
`;
export const UserDataDocument = gql`
  query UserData {
    me(is_me: true) {
      ...Customer
    }
  }
  ${CustomerFragmentDoc}
`;

/**
 * __useUserDataQuery__
 *
 * To run a query within a React component, call `useUserDataQuery` and pass it any options that fit your needs.
 * When your component renders, `useUserDataQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useUserDataQuery({
 *   variables: {
 *   },
 * });
 */
export function useUserDataQuery(
  baseOptions?: Apollo.QueryHookOptions<UserDataQuery, UserDataQueryVariables>,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<UserDataQuery, UserDataQueryVariables>(
    UserDataDocument,
    options,
  );
}
export function useUserDataLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    UserDataQuery,
    UserDataQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<UserDataQuery, UserDataQueryVariables>(
    UserDataDocument,
    options,
  );
}
export function useUserDataSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    UserDataQuery,
    UserDataQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<UserDataQuery, UserDataQueryVariables>(
    UserDataDocument,
    options,
  );
}
export type UserDataQueryHookResult = ReturnType<typeof useUserDataQuery>;
export type UserDataLazyQueryHookResult = ReturnType<
  typeof useUserDataLazyQuery
>;
export type UserDataSuspenseQueryHookResult = ReturnType<
  typeof useUserDataSuspenseQuery
>;
export type UserDataQueryResult = Apollo.QueryResult<
  UserDataQuery,
  UserDataQueryVariables
>;
export const LogoutDocument = gql`
  mutation Logout {
    logout
  }
`;
export type LogoutMutationFn = Apollo.MutationFunction<
  LogoutMutation,
  LogoutMutationVariables
>;

/**
 * __useLogoutMutation__
 *
 * To run a mutation, you first call `useLogoutMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useLogoutMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [logoutMutation, { data, loading, error }] = useLogoutMutation({
 *   variables: {
 *   },
 * });
 */
export function useLogoutMutation(
  baseOptions?: Apollo.MutationHookOptions<
    LogoutMutation,
    LogoutMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<LogoutMutation, LogoutMutationVariables>(
    LogoutDocument,
    options,
  );
}
export type LogoutMutationHookResult = ReturnType<typeof useLogoutMutation>;
export type LogoutMutationResult = Apollo.MutationResult<LogoutMutation>;
export type LogoutMutationOptions = Apollo.BaseMutationOptions<
  LogoutMutation,
  LogoutMutationVariables
>;
export const UpdateUserLanguageDocument = gql`
  mutation UpdateUserLanguage($languageAbbr: String!, $userId: Int!) {
    succes: update_user_language(language_abbr: $languageAbbr, user_id: $userId)
  }
`;
export type UpdateUserLanguageMutationFn = Apollo.MutationFunction<
  UpdateUserLanguageMutation,
  UpdateUserLanguageMutationVariables
>;

/**
 * __useUpdateUserLanguageMutation__
 *
 * To run a mutation, you first call `useUpdateUserLanguageMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useUpdateUserLanguageMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [updateUserLanguageMutation, { data, loading, error }] = useUpdateUserLanguageMutation({
 *   variables: {
 *      languageAbbr: // value for 'languageAbbr'
 *      userId: // value for 'userId'
 *   },
 * });
 */
export function useUpdateUserLanguageMutation(
  baseOptions?: Apollo.MutationHookOptions<
    UpdateUserLanguageMutation,
    UpdateUserLanguageMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    UpdateUserLanguageMutation,
    UpdateUserLanguageMutationVariables
  >(UpdateUserLanguageDocument, options);
}
export type UpdateUserLanguageMutationHookResult = ReturnType<
  typeof useUpdateUserLanguageMutation
>;
export type UpdateUserLanguageMutationResult =
  Apollo.MutationResult<UpdateUserLanguageMutation>;
export type UpdateUserLanguageMutationOptions = Apollo.BaseMutationOptions<
  UpdateUserLanguageMutation,
  UpdateUserLanguageMutationVariables
>;
