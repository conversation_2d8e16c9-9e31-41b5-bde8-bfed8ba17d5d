/** @generated THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY. */
import * as Types from '../../../../shared/api/models.gen';

import { gql } from '@apollo/client';
import * as <PERSON> from '@apollo/client';
const defaultOptions = {} as const;
export type AttachUserByInviteHashMutationVariables = Types.Exact<{
  userId: Types.Scalars['Int']['input'];
  inviteHash: Types.Scalars['String']['input'];
}>;

export type AttachUserByInviteHashMutation = {
  __typename?: 'Mutation';
  attach_user_from_invite: boolean;
};

export const AttachUserByInviteHashDocument = gql`
  mutation AttachUserByInviteHash($userId: Int!, $inviteHash: String!) {
    attach_user_from_invite(user_id: $userId, invite_hash: $inviteHash)
  }
`;
export type AttachUserByInviteHashMutationFn = Apollo.MutationFunction<
  AttachUserByInviteHashMutation,
  AttachUserByInviteHashMutationVariables
>;

/**
 * __useAttachUserByInviteHashMutation__
 *
 * To run a mutation, you first call `useAttachUserByInviteHashMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useAttachUserByInviteHashMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [attachUserByInviteHashMutation, { data, loading, error }] = useAttachUserByInviteHashMutation({
 *   variables: {
 *      userId: // value for 'userId'
 *      inviteHash: // value for 'inviteHash'
 *   },
 * });
 */
export function useAttachUserByInviteHashMutation(
  baseOptions?: Apollo.MutationHookOptions<
    AttachUserByInviteHashMutation,
    AttachUserByInviteHashMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    AttachUserByInviteHashMutation,
    AttachUserByInviteHashMutationVariables
  >(AttachUserByInviteHashDocument, options);
}
export type AttachUserByInviteHashMutationHookResult = ReturnType<
  typeof useAttachUserByInviteHashMutation
>;
export type AttachUserByInviteHashMutationResult =
  Apollo.MutationResult<AttachUserByInviteHashMutation>;
export type AttachUserByInviteHashMutationOptions = Apollo.BaseMutationOptions<
  AttachUserByInviteHashMutation,
  AttachUserByInviteHashMutationVariables
>;
