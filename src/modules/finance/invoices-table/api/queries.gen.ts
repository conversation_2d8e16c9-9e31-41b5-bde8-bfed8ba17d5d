/** @generated THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY. */
import * as Types from '../../../../shared/api/models.gen';

import { gql } from '@apollo/client';
import * as <PERSON> from '@apollo/client';
const defaultOptions = {} as const;
export type InvoicesListQueryVariables = Types.Exact<{
  page?: Types.InputMaybe<Types.Scalars['Int']['input']>;
  limit?: Types.InputMaybe<Types.Scalars['Int']['input']>;
  orderBy?: Types.InputMaybe<Types.OrderBy>;
  direction?: Types.InputMaybe<Types.Direction>;
  merchantId?: Types.InputMaybe<Types.Scalars['Int']['input']>;
}>;

export type InvoicesListQuery = {
  __typename?: 'Query';
  invoices?: {
    __typename?: 'invoicePagination';
    has_more_pages: boolean;
    data?: Array<{
      __typename?: 'Invoice';
      id: number;
      invoice_nr?: string | null;
      total_amount: number;
      due_at: string;
      url?: string | null;
    } | null> | null;
  } | null;
};

export const InvoicesListDocument = gql`
  query InvoicesList(
    $page: Int
    $limit: Int
    $orderBy: OrderBy
    $direction: Direction
    $merchantId: Int
  ) {
    invoices(
      page: $page
      limit: $limit
      orderBy: $orderBy
      direction: $direction
      merchant_id: $merchantId
    ) {
      data {
        id
        invoice_nr
        total_amount
        due_at
        url
      }
      has_more_pages
    }
  }
`;

/**
 * __useInvoicesListQuery__
 *
 * To run a query within a React component, call `useInvoicesListQuery` and pass it any options that fit your needs.
 * When your component renders, `useInvoicesListQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useInvoicesListQuery({
 *   variables: {
 *      page: // value for 'page'
 *      limit: // value for 'limit'
 *      orderBy: // value for 'orderBy'
 *      direction: // value for 'direction'
 *      merchantId: // value for 'merchantId'
 *   },
 * });
 */
export function useInvoicesListQuery(
  baseOptions?: Apollo.QueryHookOptions<
    InvoicesListQuery,
    InvoicesListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<InvoicesListQuery, InvoicesListQueryVariables>(
    InvoicesListDocument,
    options,
  );
}
export function useInvoicesListLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    InvoicesListQuery,
    InvoicesListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<InvoicesListQuery, InvoicesListQueryVariables>(
    InvoicesListDocument,
    options,
  );
}
export function useInvoicesListSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    InvoicesListQuery,
    InvoicesListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<InvoicesListQuery, InvoicesListQueryVariables>(
    InvoicesListDocument,
    options,
  );
}
export type InvoicesListQueryHookResult = ReturnType<
  typeof useInvoicesListQuery
>;
export type InvoicesListLazyQueryHookResult = ReturnType<
  typeof useInvoicesListLazyQuery
>;
export type InvoicesListSuspenseQueryHookResult = ReturnType<
  typeof useInvoicesListSuspenseQuery
>;
export type InvoicesListQueryResult = Apollo.QueryResult<
  InvoicesListQuery,
  InvoicesListQueryVariables
>;
