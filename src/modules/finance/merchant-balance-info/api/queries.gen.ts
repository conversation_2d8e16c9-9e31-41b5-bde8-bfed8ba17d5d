/** @generated THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY. */
import * as Types from '../../../../shared/api/models.gen';

import { gql } from '@apollo/client';
import * as <PERSON> from '@apollo/client';
const defaultOptions = {} as const;
export type MerchantBalanceQueryVariables = Types.Exact<{
  merchantId: Types.Scalars['Int']['input'];
  balanceAt?: Types.InputMaybe<Types.Scalars['String']['input']>;
}>;

export type MerchantBalanceQuery = {
  __typename?: 'Query';
  balance?: {
    __typename?: 'UnpaidMerchant';
    id: number;
    pending_principal: number;
    pending_bonus: number;
  } | null;
};

export const MerchantBalanceDocument = gql`
  query MerchantBalance($merchantId: Int!, $balanceAt: String) {
    balance: merchant_balance(
      merchant_id: $merchantId
      balance_at: $balanceAt
    ) {
      id
      pending_principal
      pending_bonus
    }
  }
`;

/**
 * __useMerchantBalanceQuery__
 *
 * To run a query within a React component, call `useMerchantBalanceQuery` and pass it any options that fit your needs.
 * When your component renders, `useMerchantBalanceQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useMerchantBalanceQuery({
 *   variables: {
 *      merchantId: // value for 'merchantId'
 *      balanceAt: // value for 'balanceAt'
 *   },
 * });
 */
export function useMerchantBalanceQuery(
  baseOptions: Apollo.QueryHookOptions<
    MerchantBalanceQuery,
    MerchantBalanceQueryVariables
  > &
    (
      | { variables: MerchantBalanceQueryVariables; skip?: boolean }
      | { skip: boolean }
    ),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<MerchantBalanceQuery, MerchantBalanceQueryVariables>(
    MerchantBalanceDocument,
    options,
  );
}
export function useMerchantBalanceLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    MerchantBalanceQuery,
    MerchantBalanceQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    MerchantBalanceQuery,
    MerchantBalanceQueryVariables
  >(MerchantBalanceDocument, options);
}
export function useMerchantBalanceSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    MerchantBalanceQuery,
    MerchantBalanceQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    MerchantBalanceQuery,
    MerchantBalanceQueryVariables
  >(MerchantBalanceDocument, options);
}
export type MerchantBalanceQueryHookResult = ReturnType<
  typeof useMerchantBalanceQuery
>;
export type MerchantBalanceLazyQueryHookResult = ReturnType<
  typeof useMerchantBalanceLazyQuery
>;
export type MerchantBalanceSuspenseQueryHookResult = ReturnType<
  typeof useMerchantBalanceSuspenseQuery
>;
export type MerchantBalanceQueryResult = Apollo.QueryResult<
  MerchantBalanceQuery,
  MerchantBalanceQueryVariables
>;
