import { Box, HStack, Icon, Text, VStack } from '@chakra-ui/react';
import { FiAlertTriangle, FiCheckCircle, FiInfo } from 'react-icons/fi';

import {
  type CreditEligibilityCheckResponse,
  CreditEligibilityStatus,
} from './types';

interface CreditEligibilityResultProps {
  result: CreditEligibilityCheckResponse;
}

export const CreditEligibilityResult = ({
  result,
}: CreditEligibilityResultProps) => {
  const resultConfigs = {
    [CreditEligibilityStatus.ELIGIBLE]: {
      bgColor: 'green.100',
      iconColor: 'green.900',
      textColor: 'green.900',
      icon: FiCheckCircle,
    },
    [CreditEligibilityStatus.MORE_INFO_NEEDED]: {
      bgColor: 'blue.100',
      iconColor: 'blue.900',
      textColor: 'blue.900',
      icon: FiInfo,
    },
    [CreditEligibilityStatus.NOT_ELIGIBLE]: {
      bgColor: 'red.100',
      iconColor: 'red.900',
      textColor: 'red.900',
      icon: FiAlertTriangle,
    },
  };

  const config = resultConfigs[result.status] || {
    bgColor: 'neutral.50',
    iconColor: 'neutral.700',
    textColor: 'neutral.700',
    icon: FiInfo,
  };

  return (
    <Box bg={config.bgColor} borderRadius="8px" p={4} w="full">
      <HStack spacing={4} align="flex-start">
        <Box pt={1}>
          <Icon as={config.icon} boxSize={6} color={config.iconColor} />
        </Box>
        <VStack spacing={2} align="flex-start" flex={1}>
          {result.customerName && (
            <Text
              fontSize="16px"
              fontWeight="400"
              color={
                result.status === CreditEligibilityStatus.NOT_ELIGIBLE
                  ? 'blue.900'
                  : config.textColor
              }
              lineHeight="1.5"
            >
              {result.customerName}
            </Text>
          )}
          <Text
            fontSize="16px"
            fontWeight="400"
            color={config.textColor}
            lineHeight="1.5"
          >
            {result.message}
          </Text>
        </VStack>
      </HStack>
    </Box>
  );
};
