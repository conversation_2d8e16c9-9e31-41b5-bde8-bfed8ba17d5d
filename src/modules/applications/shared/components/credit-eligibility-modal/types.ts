import { z } from 'zod';

export enum CreditEligibilityStatus {
  ELIGIBLE = 'ELIGIBLE',
  NOT_ELIGIBLE = 'NOT_ELIGIBLE',
  MORE_INFO_NEEDED = 'MORE_INFO_NEEDED',
}

export const CreditEligibilityFormSchema = z.object({
  amount: z.number().min(1, 'Amount must be greater than 0'),
  idCode: z.string().min(1, 'ID code is required'),
  email: z.string().email('Invalid email format'),
  privacyPolicyConsent: z.boolean().refine((val) => val === true, {
    message: 'Privacy policy consent is required',
  }),
  newsletterConsent: z.boolean(),
});

export type CreditEligibilityFormData = z.infer<
  typeof CreditEligibilityFormSchema
>;

export interface CreditEligibilityCheckRequest {
  amount: number;
  idCode: string;
  email: string;
  privacyPolicyConsent: boolean;
  newsletterConsent: boolean;
}

export interface CreditEligibilityCheckResponse {
  status: CreditEligibilityStatus;
  customerName?: string;
  message: string;
}

export interface CreditEligibilityModalProps {
  isOpen: boolean;
  onClose: () => void;
}
