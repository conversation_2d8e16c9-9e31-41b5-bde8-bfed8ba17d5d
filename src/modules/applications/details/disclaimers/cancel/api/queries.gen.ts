/** @generated THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY. */
import * as Types from '../../../../../../shared/api/models.gen';

import { gql } from '@apollo/client';
import * as <PERSON> from '@apollo/client';
const defaultOptions = {} as const;
export type DeleteApplicationCancellationRequestMutationVariables =
  Types.Exact<{
    requestId: Types.Scalars['Int']['input'];
  }>;

export type DeleteApplicationCancellationRequestMutation = {
  __typename?: 'Mutation';
  delete_application_cancellation_request: boolean;
};

export const DeleteApplicationCancellationRequestDocument = gql`
  mutation DeleteApplicationCancellationRequest($requestId: Int!) {
    delete_application_cancellation_request(request_id: $requestId)
  }
`;
export type DeleteApplicationCancellationRequestMutationFn =
  Apollo.MutationFunction<
    DeleteApplicationCancellationRequestMutation,
    DeleteApplicationCancellationRequestMutationVariables
  >;

/**
 * __useDeleteApplicationCancellationRequestMutation__
 *
 * To run a mutation, you first call `useDeleteApplicationCancellationRequestMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useDeleteApplicationCancellationRequestMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [deleteApplicationCancellationRequestMutation, { data, loading, error }] = useDeleteApplicationCancellationRequestMutation({
 *   variables: {
 *      requestId: // value for 'requestId'
 *   },
 * });
 */
export function useDeleteApplicationCancellationRequestMutation(
  baseOptions?: Apollo.MutationHookOptions<
    DeleteApplicationCancellationRequestMutation,
    DeleteApplicationCancellationRequestMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    DeleteApplicationCancellationRequestMutation,
    DeleteApplicationCancellationRequestMutationVariables
  >(DeleteApplicationCancellationRequestDocument, options);
}
export type DeleteApplicationCancellationRequestMutationHookResult = ReturnType<
  typeof useDeleteApplicationCancellationRequestMutation
>;
export type DeleteApplicationCancellationRequestMutationResult =
  Apollo.MutationResult<DeleteApplicationCancellationRequestMutation>;
export type DeleteApplicationCancellationRequestMutationOptions =
  Apollo.BaseMutationOptions<
    DeleteApplicationCancellationRequestMutation,
    DeleteApplicationCancellationRequestMutationVariables
  >;
