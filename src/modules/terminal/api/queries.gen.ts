/** @generated THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY. */
import * as Types from '../../../shared/api/models.gen';

import { gql } from '@apollo/client';
import * as <PERSON> from '@apollo/client';
const defaultOptions = {} as const;
export type ScheduleTypesSettingsQueryVariables = Types.Exact<{
  [key: string]: never;
}>;

export type ScheduleTypesSettingsQuery = {
  __typename?: 'Query';
  schedule_types_settings?: {
    __typename?: 'ScheduleTypesSettings';
    REGULAR: { __typename?: 'ScheduleTypeSettings'; enabled: boolean };
    ESTO_X: { __typename?: 'ScheduleTypeSettings'; enabled: boolean };
    PAY_LATER: { __typename?: 'ScheduleTypeSettings'; enabled: boolean };
    SMALL_LOAN: {
      __typename?: 'ScheduleTypeSettings';
      min_loan_amount: number;
      max_loan_amount: number;
      enabled: boolean;
      possible_periods: Array<number | null>;
    };
  } | null;
};

export const ScheduleTypesSettingsDocument = gql`
  query ScheduleTypesSettings {
    schedule_types_settings {
      REGULAR {
        enabled
      }
      ESTO_X {
        enabled
      }
      PAY_LATER {
        enabled
      }
      SMALL_LOAN {
        min_loan_amount
        max_loan_amount
        enabled
        possible_periods
      }
    }
  }
`;

/**
 * __useScheduleTypesSettingsQuery__
 *
 * To run a query within a React component, call `useScheduleTypesSettingsQuery` and pass it any options that fit your needs.
 * When your component renders, `useScheduleTypesSettingsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useScheduleTypesSettingsQuery({
 *   variables: {
 *   },
 * });
 */
export function useScheduleTypesSettingsQuery(
  baseOptions?: Apollo.QueryHookOptions<
    ScheduleTypesSettingsQuery,
    ScheduleTypesSettingsQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    ScheduleTypesSettingsQuery,
    ScheduleTypesSettingsQueryVariables
  >(ScheduleTypesSettingsDocument, options);
}
export function useScheduleTypesSettingsLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    ScheduleTypesSettingsQuery,
    ScheduleTypesSettingsQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    ScheduleTypesSettingsQuery,
    ScheduleTypesSettingsQueryVariables
  >(ScheduleTypesSettingsDocument, options);
}
export function useScheduleTypesSettingsSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    ScheduleTypesSettingsQuery,
    ScheduleTypesSettingsQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    ScheduleTypesSettingsQuery,
    ScheduleTypesSettingsQueryVariables
  >(ScheduleTypesSettingsDocument, options);
}
export type ScheduleTypesSettingsQueryHookResult = ReturnType<
  typeof useScheduleTypesSettingsQuery
>;
export type ScheduleTypesSettingsLazyQueryHookResult = ReturnType<
  typeof useScheduleTypesSettingsLazyQuery
>;
export type ScheduleTypesSettingsSuspenseQueryHookResult = ReturnType<
  typeof useScheduleTypesSettingsSuspenseQuery
>;
export type ScheduleTypesSettingsQueryResult = Apollo.QueryResult<
  ScheduleTypesSettingsQuery,
  ScheduleTypesSettingsQueryVariables
>;
