import { useApolloClient } from '@apollo/client';
import { RudderAnalytics } from '@rudderstack/analytics-js';
import { MeDocument, type MeQuery } from 'api/core/generated';
import { AppSearchParams } from 'app-constants';
import { region, rudderStackApiKey, rudderStackPlaneUrl } from 'environment';
import { AppRegions } from 'models';
import { useCallback, useMemo } from 'react';
import { useSearchParams } from 'react-router-dom';
import type { AdditionalDocumentsPendingEventData } from 'services/occupation-service';

import { useGetAnalyticsCampaignParameters } from './use-get-analytics-campaign-parameters';
import { useTrackRudderstackEvent } from './use-track-rudderstack-event';

const rudderAnalytics = new RudderAnalytics();

export const useRudderStack = () => {
  const apolloClient = useApolloClient();
  const [searchParams] = useSearchParams();
  const rlAnonymousId = searchParams.get(AppSearchParams.rlAnonymousId) || '';

  const campaignParameters = useGetAnalyticsCampaignParameters();

  const { track } = useTrackRudderstackEvent();

  const isRudderStackIsInitialized = Boolean(
    rudderStackApiKey &&
      rudderStackPlaneUrl &&
      rudderAnalytics.analyticsInstances[rudderStackApiKey]?.initialized,
  );

  const identify = useCallback(() => {
    if (!isRudderStackIsInitialized) {
      return;
    }

    const data: Nullable<MeQuery> = apolloClient.cache.read({
      query: MeDocument,
      optimistic: true,
    });

    if (!data?.me?.id) {
      return;
    }

    const userId = `${region}-${data.me.id}`;

    rudderAnalytics.identify(userId);
  }, [isRudderStackIsInitialized, apolloClient]);

  const trackEvent = useCallback(
    <T extends Record<string, unknown>>(event: string, eventData?: T) => {
      track({
        event_name: event,
        properties: JSON.stringify(eventData),
        context: {
          campaign: JSON.stringify(campaignParameters),
        },
      });
    },
    [track, campaignParameters],
  );

  const initialize = () => {
    if (isRudderStackIsInitialized) {
      return;
    }

    rudderAnalytics.load(rudderStackApiKey ?? '', rudderStackPlaneUrl ?? '', {
      plugins: ['StorageEncryption'],
      storage: {
        encryption: {
          version: 'legacy',
        },
      },
    });

    if (rlAnonymousId) {
      rudderAnalytics.setAnonymousId(rlAnonymousId);
    }

    rudderAnalytics.ready(() => {
      identify();
    });
  };

  const trackPage = useCallback(
    (pageName: string) => {
      if (isRudderStackIsInitialized) {
        rudderAnalytics.page(pageName, { country: region });
      }
    },
    [isRudderStackIsInitialized],
  );

  const ruderStackEvents = useMemo(
    () => ({
      verifyInformation: (
        data: { applicationId: number } | { creditAccountId: number },
      ) => {
        const eventName = {
          [AppRegions.EE]: 'CHECK_INCOME_VERIFY_EE',
          [AppRegions.LT]: 'CHECK_INCOME_VERIFY_LT',
          [AppRegions.LV]: 'CHECK_INCOME_VERIFY_LV',
        }[region];

        if (!eventName) {
          return;
        }

        if ('applicationId' in data) {
          trackEvent(eventName, {
            application_id: data.applicationId,
          });

          return;
        }

        if ('creditAccountId' in data) {
          trackEvent(eventName, {
            credit_account_id: data.creditAccountId,
          });
        }
      },
      marketingConsentOnSuccess: (data: { userId: number }) => {
        trackEvent('PF_SUCCESS_PAGE_MARKETING_CONSENT', {
          userId: data.userId,
        });
      },

      additionalDocumentsPending: (
        data: AdditionalDocumentsPendingEventData,
      ) => {
        trackEvent('ADDITIONAL_DOCUMENT_PENDING', data);
      },

      // Credit Eligibility Check Events
      creditEligibilityModalOpened: (data: { merchantId: number }) => {
        trackEvent('CREDIT_ELIGIBILITY_MODAL_OPENED', {
          merchant_id: data.merchantId,
        });
      },

      creditEligibilityCustomerScreened: (data: {
        merchantId: number;
        idCode: string;
        amount: number;
        status: string;
      }) => {
        trackEvent('CREDIT_ELIGIBILITY_CUSTOMER_SCREENED', {
          merchant_id: data.merchantId,
          id_code: data.idCode,
          amount: data.amount,
          eligibility_status: data.status,
        });
      },

      creditEligibilityEmailFilled: (data: {
        merchantId: number;
        idCode: string;
        email: string;
      }) => {
        trackEvent('CREDIT_ELIGIBILITY_EMAIL_FILLED', {
          merchant_id: data.merchantId,
          id_code: data.idCode,
          email: data.email,
        });
      },

      creditEligibilityConsentsCollected: (data: {
        merchantId: number;
        idCode: string;
        privacyPolicyConsent: boolean;
        newsletterConsent: boolean;
      }) => {
        trackEvent('CREDIT_ELIGIBILITY_CONSENTS_COLLECTED', {
          merchant_id: data.merchantId,
          id_code: data.idCode,
          privacy_policy_consent: data.privacyPolicyConsent,
          newsletter_consent: data.newsletterConsent,
        });
      },
    }),
    [trackEvent],
  );

  const reset = useCallback(() => {
    if (!isRudderStackIsInitialized) {
      return;
    }
    rudderAnalytics.reset();
  }, [isRudderStackIsInitialized]);

  return {
    ruderStackEvents,
    initialize,
    identify,
    trackPage,
    reset,
  };
};
