# Credit Eligibility Check Feature - Localization Keys

This document contains all localization keys used in the Credit Eligibility Check feature with their English translations from the Figma design.

## Namespace
All keys are under the `applications` namespace (`LocizeNamespaces.APPLICATIONS`).

## Localization Keys and English Translations

### Modal Title
| Key | English Translation |
|-----|-------------------|
| `credit-eligibility.title` | "Eligibility check" |

### Form Fields

#### Amount Field
| Key | English Translation |
|-----|-------------------|
| `credit-eligibility.amount` | "Amount" |

#### ID Code Field
| Key | English Translation |
|-----|-------------------|
| `credit-eligibility.id-code` | "ID code" |
| `credit-eligibility.id-code-placeholder` | "Client's personal ID code" |

#### Email Field
| Key | English Translation |
|-----|-------------------|
| `credit-eligibility.email` | "Email" |
| `credit-eligibility.email-placeholder` | "Client's email" |

### Consent Checkboxes

#### Privacy Policy Consent
| Key | English Translation |
|-----|-------------------|
| `credit-eligibility.privacy-policy-consent` | "The customer agrees to the use of their personal data for credit assessment under the ESTO's privacy policy" |

#### Newsletter Consent
| Key | English Translation |
|-----|-------------------|
| `credit-eligibility.newsletter-consent` | "The customer wishes to receive newsletters, discounts and partner offers from ESTO" |

### Action Buttons

#### Check Eligibility Button
| Key | English Translation |
|-----|-------------------|
| `credit-eligibility.check-eligibility` | "Check eligibility" |
| `credit-eligibility.checking` | "Checking..." |

### Common Modal Actions
| Key | English Translation |
|-----|-------------------|
| `common:modal.close` | "Close" |

## Implementation Notes

1. **Namespace Usage**: All keys are accessed using `LocizeApplicationsKeys` constants and translated with `useTranslation(LocizeNamespaces.APPLICATIONS)`.

2. **Key Structure**: Keys follow the pattern `credit-eligibility.{field-name}` for consistency.

3. **Placeholder Keys**: Separate keys are used for placeholder text to allow for different translations if needed.

4. **Button States**: The check eligibility button has both normal and loading states with different text.

5. **Common Keys**: The close button uses a common key from the `common` namespace for consistency across the application.

## Usage Example

```typescript
import { useTranslation } from 'react-i18next';
import { LocizeApplicationsKeys, LocizeNamespaces } from 'shared/constants/localization-keys';

const { t } = useTranslation(LocizeNamespaces.APPLICATIONS);

// Modal title
const title = t(LocizeApplicationsKeys.CREDIT_ELIGIBILITY_TITLE);

// Form field labels
const amountLabel = t(LocizeApplicationsKeys.CREDIT_ELIGIBILITY_AMOUNT);
const idCodeLabel = t(LocizeApplicationsKeys.CREDIT_ELIGIBILITY_ID_CODE);
const emailLabel = t(LocizeApplicationsKeys.CREDIT_ELIGIBILITY_EMAIL);

// Placeholders
const idCodePlaceholder = t(LocizeApplicationsKeys.CREDIT_ELIGIBILITY_ID_CODE_PLACEHOLDER);
const emailPlaceholder = t(LocizeApplicationsKeys.CREDIT_ELIGIBILITY_EMAIL_PLACEHOLDER);

// Consent text
const privacyConsent = t(LocizeApplicationsKeys.CREDIT_ELIGIBILITY_PRIVACY_POLICY_CONSENT);
const newsletterConsent = t(LocizeApplicationsKeys.CREDIT_ELIGIBILITY_NEWSLETTER_CONSENT);

// Button text
const checkButtonText = t(LocizeApplicationsKeys.CREDIT_ELIGIBILITY_CHECK_ELIGIBILITY);
const checkingText = t(LocizeApplicationsKeys.CREDIT_ELIGIBILITY_CHECKING);
```

## Rudderstack Events

The following Rudderstack events are tracked for this feature:

1. **CREDIT_ELIGIBILITY_MODAL_OPENED** - When the modal is opened
2. **CREDIT_ELIGIBILITY_CUSTOMER_SCREENED** - When eligibility check is performed
3. **CREDIT_ELIGIBILITY_EMAIL_FILLED** - When email is provided
4. **CREDIT_ELIGIBILITY_CONSENTS_COLLECTED** - When consent checkboxes are filled

These events help track user engagement and conversion rates for the credit eligibility check feature.
